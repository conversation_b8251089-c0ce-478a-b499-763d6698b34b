const HeroSection = () => {
  return (
    <section id="home" className="relative min-h-screen bg-gradient-to-br from-black to-[#253900] overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Floating geometric shapes */}
        <div className="absolute top-20 left-10 w-16 h-16 bg-[#08CB00] rounded-lg transform rotate-12 opacity-80 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-12 h-12 bg-[#08CB00] rounded-full opacity-60 animate-bounce"></div>
        <div className="absolute bottom-32 left-20 w-20 h-20 border-4 border-[#08CB00] rounded-lg transform -rotate-12 opacity-40"></div>
        <div className="absolute bottom-20 right-32 w-14 h-14 bg-[#08CB00] transform rotate-45 opacity-50"></div>
        <div className="absolute top-1/2 left-1/4 w-8 h-8 bg-[#08CB00] rounded-full opacity-30 animate-ping"></div>
        <div className="absolute top-1/3 right-1/3 w-10 h-10 border-2 border-[#08CB00] rounded-full opacity-40"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="flex flex-col lg:flex-row items-center justify-between min-h-[80vh]">
          
          {/* Left content */}
          <div className="flex-1 text-center lg:text-left mb-12 lg:mb-0 lg:pr-12">
            {/* Tagline */}
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-[#08CB00] text-black text-sm font-semibold rounded-full uppercase tracking-wide">
                Professional Video Editing Agency
              </span>
            </div>

            {/* Main headline */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Transform Your Vision Into{' '}
              <span className="relative">
                <span className="text-[#08CB00]">Stunning Videos</span>
                <svg
                  className="absolute -bottom-2 left-0 w-full h-4 text-[#08CB00]"
                  viewBox="0 0 300 20"
                  fill="none"
                >
                  <path
                    d="M5 15 Q150 5 295 15"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="none"
                  />
                </svg>
              </span>
            </h1>

            {/* Supporting text */}
            <p className="text-lg sm:text-xl text-gray-300 mb-8 max-w-2xl">
              We are a professional video editing agency helping brands, creators, and businesses craft engaging video content that captures attention and drives results.
            </p>

            {/* CTA buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button className="bg-[#08CB00] text-black px-8 py-4 rounded-lg font-semibold text-lg hover:bg-[#06A800] transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                Get Started →
              </button>
              <button className="bg-[#EEEEEE] text-black px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                View Portfolio →
              </button>
            </div>
          </div>

          {/* Right content - Video editing workspace mockup */}
          <div className="flex-1 w-full max-w-2xl">
            <div className="relative">
              {/* Main video editing interface container */}
              <div className="bg-[#EEEEEE] rounded-2xl shadow-2xl p-6 transform rotate-1 hover:rotate-0 transition-transform duration-500">

                {/* Video editor header */}
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-black">Video Editor Pro</h3>
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-[#08CB00] rounded-full"></div>
                  </div>
                </div>

                {/* Video preview area */}
                <div className="bg-black rounded-lg mb-4 aspect-video flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-4 border-2 border-[#08CB00] border-opacity-30 rounded-lg"></div>
                  <div className="w-16 h-16 bg-[#08CB00] rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-black ml-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 5v10l8-5-8-5z"/>
                    </svg>
                  </div>
                  <div className="absolute bottom-2 left-2 text-[#08CB00] text-xs font-mono">00:45 / 02:30</div>
                </div>

                {/* Timeline mockup */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-[#08CB00] rounded"></div>
                    <div className="flex-1 h-3 bg-[#253900] rounded-full relative">
                      <div className="absolute left-0 top-0 h-full w-3/4 bg-[#08CB00] rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-blue-500 rounded"></div>
                    <div className="flex-1 h-3 bg-gray-300 rounded-full relative">
                      <div className="absolute left-0 top-0 h-full w-1/2 bg-blue-500 rounded-full"></div>
                    </div>
                  </div>
                </div>

                {/* Project stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white rounded-lg p-3 border-l-4 border-[#08CB00]">
                    <div className="text-xs text-[#253900] mb-1">Projects Done</div>
                    <div className="text-xl font-bold text-black">500+</div>
                  </div>
                  <div className="bg-white rounded-lg p-3 border-l-4 border-[#08CB00]">
                    <div className="text-xs text-[#253900] mb-1">Happy Clients</div>
                    <div className="text-xl font-bold text-black">50+</div>
                  </div>
                </div>
              </div>

              {/* Floating render progress card */}
              <div className="absolute -bottom-4 -left-4 bg-white rounded-xl shadow-xl p-4 transform -rotate-2 hover:rotate-0 transition-transform duration-500 border-l-4 border-[#08CB00]">
                <div className="text-sm text-[#253900] mb-1">Rendering Progress</div>
                <div className="text-xl font-bold text-black">85%</div>
                <div className="text-xs text-gray-500">2 min remaining</div>
              </div>

              {/* Floating client satisfaction card */}
              <div className="absolute -top-4 -right-4 bg-white rounded-xl shadow-xl p-4 transform rotate-2 hover:rotate-0 transition-transform duration-500 border-l-4 border-[#08CB00]">
                <div className="text-sm text-[#253900] mb-1">Client Satisfaction</div>
                <div className="text-xl font-bold text-black">98.5%</div>
                <div className="text-xs text-[#08CB00]">5-Star Reviews</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom gradient overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-50 to-transparent"></div>
    </section>
  );
};

export default HeroSection;
