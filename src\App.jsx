import Navbar from './components/Navbar';
import HeroSection from './components/HeroSection';
import AboutSection from './components/AboutSection';
import PortfolioSection from './components/PortfolioSection';
import ServicesSection from './components/ServicesSection';
import TestimonialSection from './components/TestimonialSection';

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      {/* Hero Section */}
      <HeroSection />

      {/* About Section */}
      <AboutSection />

      {/* Portfolio Section */}
      <PortfolioSection />

      {/* Services Section */}
      <ServicesSection />

      {/* Testimonial Section */}
      <TestimonialSection />

      {/* Other sections */}
      <div className="w-full space-y-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

          <section id="why-choose-us" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Why Choose Us Section</h2>
          </section>

          <section id="contact" className="min-h-screen flex items-center justify-center">
            <h2 className="text-3xl font-bold text-[#253900]">Contact Section</h2>
          </section>
        </div>
      </div>
    </div>
  )
}

export default App
