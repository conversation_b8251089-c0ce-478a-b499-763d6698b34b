import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const menuItems = [
    { name: 'Home', href: '#home', type: 'anchor' },
    { name: 'About', href: '#about', type: 'anchor' },
    { name: 'Tools', href: '#tools', type: 'anchor' },
    { name: 'Portfolio', href: '#portfolio', type: 'anchor' },
    { name: 'Services', href: '#services', type: 'anchor' },
    { name: 'Testimonials', href: '#testimonials', type: 'anchor' },
    { name: 'Contact', href: '/contact', type: 'route' },
  ];

  const renderNavLink = (item) => {
    if (item.type === 'route') {
      return (
        <Link
          to={item.href}
          className="text-white hover:text-[#08CB00] px-3 py-2 text-sm font-medium transition-colors duration-300 relative group"
        >
          {item.name}
          <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#08CB00] transition-all duration-300 group-hover:w-full"></span>
        </Link>
      );
    } else {
      // Only show anchor links if we're on the home page
      if (location.pathname !== '/') {
        return (
          <Link
            to={`/${item.href}`}
            className="text-white hover:text-[#08CB00] px-3 py-2 text-sm font-medium transition-colors duration-300 relative group"
          >
            {item.name}
            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#08CB00] transition-all duration-300 group-hover:w-full"></span>
          </Link>
        );
      } else {
        return (
          <a
            href={item.href}
            className="text-white hover:text-[#08CB00] px-3 py-2 text-sm font-medium transition-colors duration-300 relative group"
          >
            {item.name}
            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#08CB00] transition-all duration-300 group-hover:w-full"></span>
          </a>
        );
      }
    }
  };

  return (
    <nav className="sticky top-0 z-50 bg-black shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="text-white text-xl font-bold hover:text-[#08CB00] transition-colors duration-300">
              VideoEdit Pro
            </Link>
          </div>

          {/* Desktop Menu */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              {menuItems.map((item) => renderNavLink(item))}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-white hover:text-[#08CB00] focus:outline-none focus:text-[#08CB00] transition-colors duration-300"
              aria-label="Toggle menu"
            >
              <svg
                className="h-6 w-6"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 24 24"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-black border-t border-gray-800">
              {menuItems.map((item) => (
                <div key={item.name} onClick={() => setIsMenuOpen(false)}>
                  {item.type === 'route' ? (
                    <Link
                      to={item.href}
                      className="text-white hover:text-[#08CB00] hover:bg-[#253900] block px-3 py-2 text-base font-medium transition-colors duration-300 rounded-md"
                    >
                      {item.name}
                    </Link>
                  ) : location.pathname !== '/' ? (
                    <Link
                      to={`/${item.href}`}
                      className="text-white hover:text-[#08CB00] hover:bg-[#253900] block px-3 py-2 text-base font-medium transition-colors duration-300 rounded-md"
                    >
                      {item.name}
                    </Link>
                  ) : (
                    <a
                      href={item.href}
                      className="text-white hover:text-[#08CB00] hover:bg-[#253900] block px-3 py-2 text-base font-medium transition-colors duration-300 rounded-md"
                    >
                      {item.name}
                    </a>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
