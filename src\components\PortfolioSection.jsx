import { useState, useEffect } from 'react';

const PortfolioSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Real video data from public/videos folder
  const portfolioVideos = [
    {
      id: 1,
      title: "Skin Care Tips",
      category: "Social Media",
      videoPath: "/videos/3 Tips to maintain your Skin✨Good skin care is important for the following reasons- It helps you.mp4",
      client: "Beauty Brand",
      description: "Professional skincare content with engaging visuals and smooth transitions."
    },
    {
      id: 2,
      title: "Adissia One World",
      category: "Commercial",
      videoPath: "/videos/Adissia one world  adhisayangalin aarambam.  For site visit & bookings  CONTACT- 9751153535.mp4",
      client: "Real Estate",
      description: "Real estate promotional video with dynamic property showcases."
    },
    {
      id: 3,
      title: "Ooty Stay Experience",
      category: "Travel",
      videoPath: "/videos/Best stay in Ooty! 😍🔥✨ ABIFIED GIVEAWAY ALERT! ✨🏔️Win a FREE 1-day stay at @fortuneretreats i.mp4",
      client: "Tourism",
      description: "Travel content featuring beautiful locations and engaging storytelling."
    },
    {
      id: 4,
      title: "Diwali Celebration",
      category: "Social Media",
      videoPath: "/videos/Celebrate this Diwali with your loved ones with @aroma__official 🌺🫶🏻📍Aroma Green Tree - TV S.mp4",
      client: "Lifestyle Brand",
      description: "Festival celebration video with vibrant colors and cultural elements."
    },
    {
      id: 5,
      title: "Watch Collection",
      category: "Commercial",
      videoPath: "/videos/Comment 'Watch' I will DM you the watch link! ⌚ Why a Watch is More Than Just Time — It's a Stat.mp4",
      client: "Watch Brand",
      description: "Product showcase with elegant cinematography and luxury appeal."
    },
    {
      id: 6,
      title: "IAS Success Story",
      category: "Documentary",
      videoPath: "/videos/From a coolie to IAS officer !.mp4",
      client: "Educational",
      description: "Inspirational documentary with powerful storytelling and emotional impact."
    },
    {
      id: 7,
      title: "Love Story",
      category: "Music Video",
      videoPath: "/videos/L.O.V.E.mp4",
      client: "Music Artist",
      description: "Romantic music video with cinematic visuals and smooth editing."
    },
    {
      id: 8,
      title: "Family Vlog",
      category: "Social Media",
      videoPath: "/videos/One of the most impromtu vlog ever 😂But definitely my mom is pissed off at me now! 🥲🎀And cred.mp4",
      client: "Content Creator",
      description: "Personal vlog with natural storytelling and authentic moments."
    },
    {
      id: 9,
      title: "Jewelry Collection",
      category: "Commercial",
      videoPath: "/videos/Paper casting ring at just 6_ only ( both men & women) Shop details 💍Varsha jewellers 💍Address.mp4",
      client: "Jewelry Store",
      description: "Product showcase with detailed close-ups and elegant presentation."
    },
    {
      id: 10,
      title: "Car Purchase",
      category: "Lifestyle",
      videoPath: "/videos/Rani Ka Pudhu Car Vaangitaanga 🔥🙌📍Epic Toyota , Mount Road & Vyasarpadi , Chennai.Offers Avai.mp4",
      client: "Automotive",
      description: "Car dealership content with dynamic shots and celebration moments."
    },
    {
      id: 11,
      title: "Pinterest Tips",
      category: "Educational",
      videoPath: "/videos/Stop using Pinterest…. But wait.mp4",
      client: "Digital Marketing",
      description: "Educational content with clear messaging and engaging visuals."
    },
    {
      id: 12,
      title: "Science Experiment",
      category: "Documentary",
      videoPath: "/videos/The forbidden experiment!.mp4",
      client: "Educational",
      description: "Scientific content with dramatic presentation and clear explanations."
    },
    {
      id: 13,
      title: "State Importance",
      category: "Documentary",
      videoPath: "/videos/This state is very important for India.mp4",
      client: "Educational",
      description: "Informational content with geographical insights and cultural significance."
    },
    {
      id: 14,
      title: "Business Growth 2025",
      category: "Corporate",
      videoPath: "/videos/Want to grow your business in 2025Launch your own branded mobile app — Food Delivery, Taxi, Car .mp4",
      client: "Tech Startup",
      description: "Business promotional video with modern graphics and professional presentation."
    },
    {
      id: 15,
      title: "Bali Adventure",
      category: "Travel",
      videoPath: "/videos/sneak peak of the craziest ATV experience from the best vacay that I had -) More Bali content co.mp4",
      client: "Travel Blogger",
      description: "Adventure travel content with exciting action shots and scenic views."
    },
    {
      id: 16,
      title: "Republic Day Celebration",
      category: "Social Media",
      videoPath: "/videos/❌Don't miss this! 😳🤯🇮🇳 Celebrate Republic Day with DecathlonPride in Play, Pride in India be.mp4",
      client: "Sports Brand",
      description: "Patriotic content with energetic visuals and national pride themes."
    },
    {
      id: 17,
      title: "Creative Tricks",
      category: "Educational",
      videoPath: "/videos/💥😳 Intha Trick Ungalaku Theriyuma🙄😍Follow @getabified for more interesting content 🔥‼️#Tric.mp4",
      client: "Content Creator",
      description: "Tutorial content with engaging demonstrations and clear instructions."
    },
    {
      id: 18,
      title: "Website Modernization",
      category: "Corporate",
      videoPath: "/videos/💻 Still stuck in 2010 like CSK's game planYour website might be costing you more than you think.mp4",
      client: "Web Agency",
      description: "Tech-focused content with modern design elements and professional messaging."
    }
  ];

  const videosPerSlide = 6;
  const totalSlides = Math.ceil(portfolioVideos.length / videosPerSlide);

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying) {
      const interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % totalSlides);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlaying, totalSlides]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
  };

  const getCurrentVideos = () => {
    const startIndex = currentSlide * videosPerSlide;
    return portfolioVideos.slice(startIndex, startIndex + videosPerSlide);
  };

  const openVideoModal = (video) => {
    setSelectedVideo(video);
    setIsModalOpen(true);
    setIsAutoPlaying(false); // Pause carousel when modal opens
  };

  const closeVideoModal = () => {
    setSelectedVideo(null);
    setIsModalOpen(false);
  };

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeVideoModal();
      }
    };

    if (isModalOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isModalOpen]);

  return (
    <section id="portfolio" className="relative py-20 bg-gradient-to-br from-black via-[#253900] to-black overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-32 left-20 w-32 h-32 border border-[#08CB00] rounded-full opacity-10 animate-spin-slow"></div>
        <div className="absolute bottom-40 right-16 w-24 h-24 bg-[#08CB00] rounded-lg transform rotate-45 opacity-5"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border-2 border-[#08CB00] rounded-full opacity-5"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="mb-4">
            <span className="inline-block px-4 py-2 bg-[#08CB00] text-black text-sm font-semibold rounded-full uppercase tracking-wide">
              Our Creative Works
            </span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Portfolio{' '}
            <span className="relative text-[#08CB00]">
              Showcase
              <svg 
                className="absolute -bottom-2 left-0 w-full h-3 text-[#08CB00]" 
                viewBox="0 0 200 12" 
                fill="none"
              >
                <path 
                  d="M5 8 Q100 2 195 8" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  fill="none"
                />
              </svg>
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Discover our latest video editing projects that showcase creativity, technical excellence, and storytelling mastery.
          </p>
        </div>

        {/* Carousel container */}
        <div className="relative">
          {/* Navigation buttons */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-20 bg-[#08CB00] text-black w-12 h-12 rounded-full flex items-center justify-center hover:bg-[#06A800] transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-20 bg-[#08CB00] text-black w-12 h-12 rounded-full flex items-center justify-center hover:bg-[#06A800] transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Carousel content */}
          <div className="overflow-hidden rounded-2xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {Array.from({ length: totalSlides }).map((_, slideIndex) => (
                <div key={slideIndex} className="w-full flex-shrink-0">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 p-4">
                    {portfolioVideos
                      .slice(slideIndex * videosPerSlide, (slideIndex + 1) * videosPerSlide)
                      .map((video) => (
                        <div
                          key={video.id}
                          className="group relative bg-[#253900] rounded-xl overflow-hidden border-2 border-transparent hover:border-[#08CB00] transition-all duration-300 transform hover:scale-105 cursor-pointer"
                          onClick={() => openVideoModal(video)}
                        >
                          {/* Actual Video */}
                          <div className="aspect-[9/16] relative overflow-hidden">
                            <video
                              className="w-full h-full object-cover"
                              muted
                              loop
                              preload="metadata"
                              onMouseEnter={(e) => e.target.play()}
                              onMouseLeave={(e) => {
                                e.target.pause();
                                e.target.currentTime = 0;
                              }}
                            >
                              <source src={video.videoPath} type="video/mp4" />
                              Your browser does not support the video tag.
                            </video>

                            {/* Play button overlay */}
                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <div className="w-16 h-16 bg-[#08CB00] rounded-full flex items-center justify-center transform scale-75 group-hover:scale-100 transition-transform duration-300">
                                <svg className="w-8 h-8 text-black ml-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M8 5v10l8-5-8-5z"/>
                                </svg>
                              </div>
                            </div>

                            {/* Video info overlay */}
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3">
                              <div className="text-white text-sm font-medium mb-1">{video.title}</div>
                              <div className="flex justify-between items-center text-xs text-gray-300">
                                <span>{video.category}</span>
                              </div>
                            </div>

                            {/* Category badge */}
                            <div className="absolute top-2 left-2">
                              <span className="bg-[#08CB00] text-black text-xs font-semibold px-2 py-1 rounded-full">
                                {video.category}
                              </span>
                            </div>
                          </div>

                          {/* Video details */}
                          <div className="p-3">
                            <h4 className="text-white font-semibold text-sm mb-1">{video.title}</h4>
                            <p className="text-gray-400 text-xs mb-2">{video.description}</p>
                            <div className="flex justify-between items-center text-xs text-gray-500">
                              <span>Client: {video.client}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Slide indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: totalSlides }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide 
                    ? 'bg-[#08CB00] w-8' 
                    : 'bg-gray-600 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>

          {/* Auto-play toggle */}
          <div className="flex justify-center mt-6">
            <button
              onClick={() => setIsAutoPlaying(!isAutoPlaying)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                isAutoPlaying 
                  ? 'bg-[#08CB00] text-black hover:bg-[#06A800]' 
                  : 'bg-gray-700 text-white hover:bg-gray-600'
              }`}
            >
              {isAutoPlaying ? 'Pause Auto-play' : 'Resume Auto-play'}
            </button>
          </div>
        </div>

        {/* Bottom stats */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">500+</div>
            <div className="text-gray-300">Projects Completed</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">50+</div>
            <div className="text-gray-300">Happy Clients</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">5+</div>
            <div className="text-gray-300">Years Experience</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-[#08CB00] mb-2">24/7</div>
            <div className="text-gray-300">Support Available</div>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {isModalOpen && selectedVideo && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 p-4">
          {/* Modal Content */}
          <div className="relative w-full max-w-4xl bg-[#253900] rounded-2xl overflow-hidden border-2 border-[#08CB00]">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-[#08CB00] border-opacity-20">
              <div>
                <h3 className="text-xl font-bold text-white">{selectedVideo.title}</h3>
                <p className="text-gray-300 text-sm">{selectedVideo.category} • Client: {selectedVideo.client}</p>
              </div>
              <button
                onClick={closeVideoModal}
                className="w-10 h-10 bg-[#08CB00] text-black rounded-full flex items-center justify-center hover:bg-[#06A800] transition-colors duration-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
            </div>

            {/* Video Player */}
            <div className="relative bg-black">
              <video
                className="w-full h-auto max-h-[70vh] object-contain"
                controls
                autoPlay
                preload="metadata"
                onLoadedData={(e) => {
                  // Reset video to beginning when modal opens
                  e.target.currentTime = 0;
                }}
              >
                <source src={selectedVideo.videoPath} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>

            {/* Video Info */}
            <div className="p-6">
              <p className="text-gray-300 text-sm leading-relaxed mb-4">
                {selectedVideo.description}
              </p>
              <div className="flex flex-wrap gap-4 text-sm">
                <div className="flex items-center">
                  <span className="text-gray-400 mr-2">Category:</span>
                  <span className="bg-[#08CB00] text-black px-2 py-1 rounded-full text-xs font-semibold">
                    {selectedVideo.category}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className="text-gray-400 mr-2">Client:</span>
                  <span className="text-white font-medium">{selectedVideo.client}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={closeVideoModal}
          ></div>
        </div>
      )}
    </section>
  );
};

export default PortfolioSection;
