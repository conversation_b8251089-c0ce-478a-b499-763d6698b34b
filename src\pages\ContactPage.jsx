import { useState } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    projectType: '',
    budget: '',
    message: '',
    timeline: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        projectType: '',
        budget: '',
        message: '',
        timeline: ''
      });
      
      // Reset success message after 3 seconds
      setTimeout(() => setSubmitStatus(''), 3000);
    }, 1500);
  };

  const projectTypes = [
    'Social Media Content',
    'Commercial/Advertisement',
    'Music Video',
    'Corporate Video',
    'Wedding/Event',
    'Documentary',
    'YouTube Content',
    'Other'
  ];

  const budgetRanges = [
    'Under $500',
    '$500 - $1,000',
    '$1,000 - $2,500',
    '$2,500 - $5,000',
    '$5,000 - $10,000',
    'Above $10,000'
  ];

  const timelineOptions = [
    'Rush (1-2 days)',
    'Standard (3-5 days)',
    'Extended (1-2 weeks)',
    'Flexible'
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Hero Section for Contact Page */}
      <section className="relative pt-20 pb-12 bg-gradient-to-br from-black to-[#253900] overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-16 h-16 bg-[#08CB00] rounded-lg transform rotate-12 opacity-80 animate-pulse"></div>
          <div className="absolute top-40 right-20 w-12 h-12 bg-[#08CB00] rounded-full opacity-60 animate-bounce"></div>
          <div className="absolute bottom-20 left-20 w-20 h-20 border-4 border-[#08CB00] rounded-lg transform -rotate-12 opacity-40"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="mb-4">
              <span className="inline-block px-4 py-2 bg-[#08CB00] text-black text-sm font-semibold rounded-full uppercase tracking-wide">
                Get In Touch
              </span>
            </div>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Let's Create Something{' '}
              <span className="relative text-[#08CB00]">
                Amazing Together
                <svg 
                  className="absolute -bottom-2 left-0 w-full h-4 text-[#08CB00]" 
                  viewBox="0 0 300 20" 
                  fill="none"
                >
                  <path 
                    d="M5 15 Q150 5 295 15" 
                    stroke="currentColor" 
                    strokeWidth="3" 
                    fill="none"
                  />
                </svg>
              </span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto">
              Ready to bring your vision to life? Tell us about your project and let's discuss how we can help you create stunning video content that engages your audience and drives results.
            </p>
          </div>
        </div>
      </section>

      {/* Main Contact Section */}
      <section className="relative py-20 bg-gradient-to-br from-black via-[#253900] to-black overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0">
          <div className="absolute top-32 left-20 w-32 h-32 border border-[#08CB00] rounded-full opacity-10 animate-pulse"></div>
          <div className="absolute bottom-40 right-16 w-24 h-24 bg-[#08CB00] rounded-lg transform rotate-45 opacity-5"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border-2 border-[#08CB00] rounded-full opacity-5"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-[#EEEEEE] rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-black mb-6">Start Your Project</h3>
              
              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-[#08CB00] text-black rounded-lg">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                    </svg>
                    Thank you! We'll get back to you within 24 hours.
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name and Email */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Full Name *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#08CB00] focus:ring-2 focus:ring-[#08CB00] focus:ring-opacity-20 transition-all duration-300"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#08CB00] focus:ring-2 focus:ring-[#08CB00] focus:ring-opacity-20 transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                {/* Phone and Project Type */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#08CB00] focus:ring-2 focus:ring-[#08CB00] focus:ring-opacity-20 transition-all duration-300"
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Project Type *</label>
                    <select
                      name="projectType"
                      value={formData.projectType}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#08CB00] focus:ring-2 focus:ring-[#08CB00] focus:ring-opacity-20 transition-all duration-300"
                    >
                      <option value="">Select project type</option>
                      {projectTypes.map((type) => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Budget and Timeline */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Budget Range</label>
                    <select
                      name="budget"
                      value={formData.budget}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#08CB00] focus:ring-2 focus:ring-[#08CB00] focus:ring-opacity-20 transition-all duration-300"
                    >
                      <option value="">Select budget range</option>
                      {budgetRanges.map((range) => (
                        <option key={range} value={range}>{range}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-black mb-2">Timeline</label>
                    <select
                      name="timeline"
                      value={formData.timeline}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#08CB00] focus:ring-2 focus:ring-[#08CB00] focus:ring-opacity-20 transition-all duration-300"
                    >
                      <option value="">Select timeline</option>
                      {timelineOptions.map((option) => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-black mb-2">Project Details *</label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows="5"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#08CB00] focus:ring-2 focus:ring-[#08CB00] focus:ring-opacity-20 transition-all duration-300 resize-none"
                    placeholder="Tell us about your project, vision, and any specific requirements..."
                  ></textarea>
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 ${
                    isSubmitting
                      ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                      : 'bg-[#08CB00] text-black hover:bg-[#06A800] shadow-lg hover:shadow-xl'
                  }`}
                >
                  {isSubmitting ? 'Sending Message...' : 'Send Message & Get Quote'}
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* Contact Details */}
              <div className="bg-[#253900] rounded-2xl p-8 border-2 border-[#08CB00]">
                <h3 className="text-2xl font-bold text-white mb-6">Get In Touch</h3>
                
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#08CB00] rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                      <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-1">Email Us</h4>
                      <p className="text-gray-300"><EMAIL></p>
                      <p className="text-gray-300"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#08CB00] rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                      <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-1">Call Us</h4>
                      <p className="text-gray-300">+****************</p>
                      <p className="text-gray-300">Mon - Fri: 9AM - 6PM EST</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-[#08CB00] rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                      <svg className="w-6 h-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-1">Visit Us</h4>
                      <p className="text-gray-300">123 Creative Street</p>
                      <p className="text-gray-300">Los Angeles, CA 90210</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Why Choose Us */}
              <div className="bg-[#EEEEEE] rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-black mb-6">Why Choose Us?</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-[#08CB00] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <span className="text-black font-medium">500+ Successful Projects</span>
                  </div>
                  
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-[#08CB00] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <span className="text-black font-medium">24-48 Hour Response Time</span>
                  </div>
                  
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-[#08CB00] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <span className="text-black font-medium">Unlimited Revisions</span>
                  </div>
                  
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-[#08CB00] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <span className="text-black font-medium">Professional Team of Editors</span>
                  </div>
                  
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-[#08CB00] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <span className="text-black font-medium">Secure File Handling</span>
                  </div>

                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-[#08CB00] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <span className="text-black font-medium">Fast Turnaround Times</span>
                  </div>
                </div>
              </div>

              {/* Quick Contact */}
              <div className="bg-[#08CB00] rounded-2xl p-6 text-center">
                <h4 className="text-xl font-bold text-black mb-3">Need Immediate Help?</h4>
                <p className="text-black mb-4">Call us directly for urgent projects or quick questions</p>
                <a
                  href="tel:+15551234567"
                  className="inline-block bg-black text-[#08CB00] px-6 py-3 rounded-lg font-semibold hover:bg-[#253900] transition-colors duration-300"
                >
                  Call Now: +****************
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ContactPage;
