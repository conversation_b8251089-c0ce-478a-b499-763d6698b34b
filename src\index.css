@tailwind base;
@tailwind components;
@tailwind utilities;

*{
  scroll-behavior: smooth;
}

/* Custom CSS Variables for Brand Colors */
:root {
  --primary-green: #08CB00;
  --dark-green: #253900;
  --black: #000000;
  --light-gray: #EEEEEE;
  --white: #FFFFFF;
}

/* Custom animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

/* Infinite scroll animations for testimonials */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll-left {
  animation: scroll-left 30s linear infinite;
}

.animate-scroll-right {
  animation: scroll-right 30s linear infinite;
}

/* Responsive scroll speed for mobile */
@media (max-width: 768px) {
  .animate-scroll-left {
    animation: scroll-left 40s linear infinite;
  }

  .animate-scroll-right {
    animation: scroll-right 40s linear infinite;
  }
}
