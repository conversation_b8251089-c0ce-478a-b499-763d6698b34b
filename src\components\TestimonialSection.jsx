const TestimonialSection = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Content Creator",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "My video editing needs were perfectly handled by this team. They transformed my raw footage into stunning content that significantly boosted my social media engagement."
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Marketing Director",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "It's simple. This team makes me more productive. Whether I need promotional videos or social media content, they deliver faster and with fewer revisions."
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Small Business Owner",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "I have been working with this video editing team for quite some time now and have been absolutely loving every part of the experience. Super intuitive!"
    },
    {
      id: 4,
      name: "<PERSON>",
      role: "YouTuber",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Super intuitive! The editing quality is amazing and I love their creative approach. They understand exactly what I need for my YouTube channel."
    },
    {
      id: 5,
      name: "Lisa Park",
      role: "Brand Manager",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "I chose this video editing service after trying several others. The quality is exceptional, turnaround times are fast, and they really understand brand storytelling."
    },
    {
      id: 6,
      name: "James Wilson",
      role: "Event Organizer",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Working with this team has been a game-changer for our event documentation. They capture the essence of every moment and create amazing highlight reels."
    },
    {
      id: 7,
      name: "Amanda Foster",
      role: "Wedding Planner",
      avatar: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Their wedding video editing is absolutely stunning. Every couple is thrilled with the final product. The storytelling and emotional impact is incredible."
    },
    {
      id: 8,
      name: "Robert Kim",
      role: "Corporate Trainer",
      avatar: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Professional training videos that engage our employees. The team understands corporate communication and delivers polished, effective content every time."
    }
  ];

  // Split testimonials into two rows for infinite scroll
  const firstRowTestimonials = testimonials.slice(0, 4);
  const secondRowTestimonials = testimonials.slice(4, 8);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <svg
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-[#08CB00]' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  const TestimonialCard = ({ testimonial }) => (
    <div className="flex-shrink-0 w-80 sm:w-96 mx-4 bg-[#EEEEEE] rounded-2xl p-6 shadow-xl border-2 border-transparent hover:border-[#08CB00] transition-all duration-300">
      {/* Quote icon */}
      <div className="mb-4">
        <svg className="w-8 h-8 text-[#08CB00] opacity-50" fill="currentColor" viewBox="0 0 24 24">
          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
        </svg>
      </div>

      {/* Testimonial text */}
      <p className="text-black text-sm leading-relaxed mb-6 font-medium">
        "{testimonial.text}"
      </p>

      {/* Rating */}
      <div className="flex items-center mb-4">
        {renderStars(testimonial.rating)}
      </div>

      {/* Author info */}
      <div className="flex items-center">
        <div className="w-12 h-12 rounded-full overflow-hidden mr-4 ring-2 ring-[#08CB00] ring-opacity-30">
          <img
            src={testimonial.avatar}
            alt={testimonial.name}
            className="w-full h-full object-cover"
          />
        </div>
        <div>
          <h4 className="font-bold text-black text-sm">{testimonial.name}</h4>
          <p className="text-[#253900] text-xs font-medium">{testimonial.role}</p>
        </div>
      </div>
    </div>
  );

  return (
    <section className="relative py-20 bg-gradient-to-br from-black via-[#253900] to-black overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-32 left-20 w-32 h-32 border border-[#08CB00] rounded-full opacity-10 animate-pulse"></div>
        <div className="absolute bottom-40 right-16 w-24 h-24 bg-[#08CB00] rounded-lg transform rotate-45 opacity-5"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border-2 border-[#08CB00] rounded-full opacity-5"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="mb-4">
            <span className="inline-block px-4 py-2 bg-[#08CB00] text-black text-sm font-semibold rounded-full uppercase tracking-wide">
              Testimonials
            </span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Professionals use Our Services to{' '}
            <span className="relative text-[#08CB00]">
              10x their Productivity
              <svg
                className="absolute -bottom-2 left-0 w-full h-3 text-[#08CB00]"
                viewBox="0 0 200 12"
                fill="none"
              >
                <path
                  d="M5 8 Q100 2 195 8"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </span>
          </h2>
        </div>

        {/* Infinite scrolling testimonials */}
        <div className="space-y-8">
          {/* First row - Left to Right */}
          <div className="relative overflow-hidden">
            <div className="flex animate-scroll-left">
              {/* First set */}
              {firstRowTestimonials.map((testimonial) => (
                <TestimonialCard key={`first-${testimonial.id}`} testimonial={testimonial} />
              ))}
              {/* Duplicate set for seamless loop */}
              {firstRowTestimonials.map((testimonial) => (
                <TestimonialCard key={`first-dup-${testimonial.id}`} testimonial={testimonial} />
              ))}
            </div>
          </div>

          {/* Second row - Right to Left */}
          <div className="relative overflow-hidden">
            <div className="flex animate-scroll-right">
              {/* First set */}
              {secondRowTestimonials.map((testimonial) => (
                <TestimonialCard key={`second-${testimonial.id}`} testimonial={testimonial} />
              ))}
              {/* Duplicate set for seamless loop */}
              {secondRowTestimonials.map((testimonial) => (
                <TestimonialCard key={`second-dup-${testimonial.id}`} testimonial={testimonial} />
              ))}
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 bg-[#253900] rounded-2xl p-6 border border-[#08CB00] border-opacity-20">
            <div className="text-white text-center sm:text-left">
              <h3 className="text-xl font-bold mb-2">Ready to Transform Your Content?</h3>
              <p className="text-gray-300 text-sm">Join hundreds of satisfied clients who trust us with their video editing needs.</p>
            </div>
            <button className="bg-[#08CB00] text-black px-6 py-3 rounded-xl font-semibold hover:bg-[#06A800] transition-colors duration-300 whitespace-nowrap">
              Get Started Today
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;
