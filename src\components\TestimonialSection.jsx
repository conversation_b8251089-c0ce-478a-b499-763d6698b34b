const TestimonialSection = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Content Creator",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "My video editing needs were perfectly handled by this team. They transformed my raw footage into stunning content that significantly boosted my social media engagement. The attention to detail and creative vision exceeded my expectations."
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Marketing Director",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "It's simple. This team makes me more productive. Whether I need promotional videos, product demos, or social media content, they deliver faster and with fewer revisions. It's one of those services that's truly game-changing for our marketing campaigns."
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Small Business Owner",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "I have been working with this video editing team for quite some time now and have been absolutely loving every part of the experience. Super intuitive communication and they never miss deadlines."
    },
    {
      id: 4,
      name: "<PERSON>",
      role: "YouTuber",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Super intuitive! The editing quality is amazing and I love their creative approach. They understand exactly what I need for my YouTube channel and consistently deliver content that keeps my audience engaged."
    },
    {
      id: 5,
      name: "Lisa Park",
      role: "Brand Manager",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "I chose this video editing service after trying several others, and I'm so glad I did. The quality is exceptional, turnaround times are fast, and they really understand brand storytelling. Highly recommended for any business."
    },
    {
      id: 6,
      name: "James Wilson",
      role: "Event Organizer",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Working with this team has been a game-changer for our event documentation. They capture the essence of every moment and create highlight reels that our clients absolutely love. Professional, creative, and reliable."
    }
  ];

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <svg
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <section className="relative py-20 bg-gradient-to-br from-[#1a1a2e] via-[#16213e] to-[#0f0f23] overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-32 left-20 w-32 h-32 border border-[#08CB00] rounded-full opacity-10 animate-pulse"></div>
        <div className="absolute bottom-40 right-16 w-24 h-24 bg-[#08CB00] rounded-lg transform rotate-45 opacity-5"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border-2 border-[#08CB00] rounded-full opacity-5"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="mb-4">
            <span className="inline-block px-4 py-2 bg-[#08CB00] text-black text-sm font-semibold rounded-full uppercase tracking-wide">
              Testimonial
            </span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Professionals use Our Services to{' '}
            <span className="relative text-[#08CB00]">
              10x their Productivity
              <svg 
                className="absolute -bottom-2 left-0 w-full h-3 text-[#08CB00]" 
                viewBox="0 0 200 12" 
                fill="none"
              >
                <path 
                  d="M5 8 Q100 2 195 8" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  fill="none"
                />
              </svg>
            </span>
          </h2>
        </div>

        {/* Testimonials grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-white rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group"
            >
              {/* Testimonial text */}
              <div className="mb-6">
                <p className="text-gray-700 text-sm leading-relaxed">
                  "{testimonial.text}"
                </p>
              </div>

              {/* Rating */}
              <div className="flex items-center mb-4">
                {renderStars(testimonial.rating)}
              </div>

              {/* Author info */}
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4 ring-2 ring-[#08CB00] ring-opacity-20 group-hover:ring-opacity-50 transition-all duration-300">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 text-sm">{testimonial.name}</h4>
                  <p className="text-gray-600 text-xs">{testimonial.role}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-4 bg-[#253900] rounded-2xl p-6 border border-[#08CB00] border-opacity-20">
            <div className="text-white">
              <h3 className="text-xl font-bold mb-2">Ready to Transform Your Content?</h3>
              <p className="text-gray-300 text-sm">Join hundreds of satisfied clients who trust us with their video editing needs.</p>
            </div>
            <button className="bg-[#08CB00] text-black px-6 py-3 rounded-xl font-semibold hover:bg-[#06A800] transition-colors duration-300 whitespace-nowrap">
              Get Started Today
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;
