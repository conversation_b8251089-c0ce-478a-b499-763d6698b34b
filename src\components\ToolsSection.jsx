const ToolsSection = () => {
  const tools = [
    {
      id: 1,
      name: "Adobe Premiere Pro",
      category: "Video Editing",
      description: "Professional video editing and timeline management",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
          <span className="text-white font-bold text-xl">Pr</span>
        </div>
      ),
      expertise: "Expert"
    },
    {
      id: 2,
      name: "Final Cut Pro",
      category: "Video Editing",
      description: "Advanced video editing for Mac workflows",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-gray-800 to-gray-600 rounded-xl flex items-center justify-center">
          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
          </svg>
        </div>
      ),
      expertise: "Expert"
    },
    {
      id: 3,
      name: "After Effects",
      category: "Motion Graphics",
      description: "Advanced motion graphics and visual effects",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-purple-800 to-pink-600 rounded-xl flex items-center justify-center">
          <span className="text-white font-bold text-xl">Ae</span>
        </div>
      ),
      expertise: "Expert"
    },
    {
      id: 4,
      name: "DaVinci Resolve",
      category: "Color Grading",
      description: "Professional color grading and correction",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-red-600 to-orange-500 rounded-xl flex items-center justify-center">
          <span className="text-white font-bold text-xl">DV</span>
        </div>
      ),
      expertise: "Expert"
    },
    {
      id: 5,
      name: "Adobe Audition",
      category: "Audio Editing",
      description: "Professional audio editing and sound design",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-teal-500 rounded-xl flex items-center justify-center">
          <span className="text-white font-bold text-xl">Au</span>
        </div>
      ),
      expertise: "Advanced"
    },
    {
      id: 6,
      name: "Blender",
      category: "3D & Animation",
      description: "3D modeling, animation, and motion graphics",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-orange-600 to-yellow-500 rounded-xl flex items-center justify-center">
          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12.51 1.46l10.74 6.2v12.68l-10.74 6.2L1.77 20.34V7.66L12.51 1.46z"/>
          </svg>
        </div>
      ),
      expertise: "Advanced"
    },
    {
      id: 7,
      name: "Adobe Photoshop",
      category: "Graphics & Thumbnails",
      description: "Video thumbnails, graphics, and visual assets",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-cyan-500 rounded-xl flex items-center justify-center">
          <span className="text-white font-bold text-xl">Ps</span>
        </div>
      ),
      expertise: "Expert"
    },
    {
      id: 8,
      name: "Cinema 4D",
      category: "3D Graphics",
      description: "Advanced 3D graphics and motion design",
      icon: (
        <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
          <span className="text-white font-bold text-xl">C4D</span>
        </div>
      ),
      expertise: "Advanced"
    }
  ];

  const getExpertiseBadgeColor = (expertise) => {
    switch (expertise) {
      case 'Expert':
        return 'bg-[#08CB00] text-black';
      case 'Advanced':
        return 'bg-[#253900] text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  return (
    <section className="relative py-20 bg-gradient-to-br from-[#253900] via-black to-[#253900] overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-24 h-24 border-2 border-[#08CB00] rounded-lg transform rotate-12 opacity-10 animate-pulse"></div>
        <div className="absolute bottom-32 right-20 w-20 h-20 bg-[#08CB00] rounded-full opacity-5 animate-bounce"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-[#08CB00] rounded-full opacity-15"></div>
        <div className="absolute bottom-20 left-1/3 w-12 h-12 bg-[#08CB00] transform rotate-45 opacity-10"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="mb-4">
            <span className="inline-block px-4 py-2 bg-[#08CB00] text-black text-sm font-semibold rounded-full uppercase tracking-wide">
              Professional Tools
            </span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Our Tools &{' '}
            <span className="relative text-[#08CB00]">
              Expertise
              <svg 
                className="absolute -bottom-2 left-0 w-full h-3 text-[#08CB00]" 
                viewBox="0 0 200 12" 
                fill="none"
              >
                <path 
                  d="M5 8 Q100 2 195 8" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  fill="none"
                />
              </svg>
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            We are experts in industry-leading video editing tools, ensuring your projects are produced with the highest quality and creativity.
          </p>
        </div>

        {/* Tools grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-16">
          {tools.map((tool) => (
            <div
              key={tool.id}
              className="group bg-[#EEEEEE] rounded-2xl p-6 border-2 border-transparent hover:border-[#08CB00] transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
            >
              {/* Tool icon */}
              <div className="flex justify-center mb-4">
                {tool.icon}
              </div>

              {/* Expertise badge */}
              <div className="flex justify-center mb-3">
                <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getExpertiseBadgeColor(tool.expertise)}`}>
                  {tool.expertise}
                </span>
              </div>

              {/* Tool info */}
              <div className="text-center">
                <h3 className="text-lg font-bold text-black mb-2">{tool.name}</h3>
                <p className="text-[#253900] text-xs font-medium mb-2">{tool.category}</p>
                <p className="text-gray-600 text-sm leading-relaxed">{tool.description}</p>
              </div>

              {/* Hover effect indicator */}
              <div className="mt-4 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-8 h-1 bg-[#08CB00] rounded-full"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom stats section */}
        <div className="bg-[#EEEEEE] rounded-2xl p-8 border-2 border-[#08CB00] border-opacity-20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-[#08CB00] mb-2">8+</div>
              <div className="text-black font-medium">Professional Tools</div>
              <div className="text-gray-600 text-sm">Industry Standard</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-[#08CB00] mb-2">5+</div>
              <div className="text-black font-medium">Years Experience</div>
              <div className="text-gray-600 text-sm">Per Tool</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-[#08CB00] mb-2">100%</div>
              <div className="text-black font-medium">Up-to-Date</div>
              <div className="text-gray-600 text-sm">Latest Versions</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-[#08CB00] mb-2">24/7</div>
              <div className="text-black font-medium">Tool Access</div>
              <div className="text-gray-600 text-sm">Always Ready</div>
            </div>
          </div>
        </div>

        {/* Call to action */}
        <div className="text-center mt-12">
          <div className="inline-flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button className="bg-[#08CB00] text-black px-8 py-4 rounded-lg font-semibold text-lg hover:bg-[#06A800] transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
              Start Your Project
            </button>
            <button className="bg-transparent border-2 border-[#08CB00] text-[#08CB00] px-8 py-4 rounded-lg font-semibold text-lg hover:bg-[#08CB00] hover:text-black transform hover:scale-105 transition-all duration-300">
              See Our Portfolio
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ToolsSection;
